{"Version": 1, "WorkspaceRootPath": "D:\\01 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6FD32753-FF41-4714-9A70-9C3194762EF3}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "AirMonitor\\ViewModels\\MainWindowViewModel.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "AirMonitor\\ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T05:23:05.739Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml.cs*", "RelativeToolTip": "AirMonitor\\MainWindow.xaml.cs*", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T05:23:15.818Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\App.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\App.xaml.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\App.xaml.cs*", "RelativeToolTip": "AirMonitor\\App.xaml.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T05:23:00.322Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AssemblyInfo.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\AssemblyInfo.cs", "RelativeDocumentMoniker": "AirMonitor\\AssemblyInfo.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\AssemblyInfo.cs", "RelativeToolTip": "AirMonitor\\AssemblyInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T05:22:58.355Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "App.xaml", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\App.xaml", "RelativeDocumentMoniker": "AirMonitor\\App.xaml", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\App.xaml", "RelativeToolTip": "AirMonitor\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-18T05:21:24.324Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-18T02:44:55.708Z"}]}]}]}