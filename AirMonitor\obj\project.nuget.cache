{"version": 2, "dgSpecHash": "I3i8zSbE2WQ=", "success": true, "projectFilePath": "D:\\01 AirMonitor\\AirMonitor\\AirMonitor.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.6\\microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.6\\microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.licensing\\29.2.11\\syncfusion.licensing.29.2.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.sfskinmanager.wpf\\29.2.11\\syncfusion.sfskinmanager.wpf.29.2.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.shared.wpf\\29.2.11\\syncfusion.shared.wpf.29.2.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.themes.windows11light.wpf\\29.2.11\\syncfusion.themes.windows11light.wpf.29.2.11.nupkg.sha512"], "logs": []}