{"version": 3, "targets": {"net8.0-windows7.0": {"Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Syncfusion.Licensing/29.2.11": {"type": "package", "compile": {"lib/net8.0/Syncfusion.Licensing.dll": {}}, "runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {}}}, "Syncfusion.SfSkinManager.WPF/29.2.11": {"type": "package", "dependencies": {"Syncfusion.Licensing": "29.2.11"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Shared.WPF/29.2.11": {"type": "package", "dependencies": {"Syncfusion.Licensing": "29.2.11"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.Windows11Light.WPF/29.2.11": {"type": "package", "dependencies": {"Syncfusion.Licensing": "29.2.11", "Syncfusion.SfSkinManager.WPF": "29.2.11"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.Windows11Light.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.Windows11Light.WPF.dll": {"related": ".xml"}}}}}, "libraries": {"Microsoft.Extensions.DependencyInjection/9.0.6": {"sha512": "vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"sha512": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Syncfusion.Licensing/29.2.11": {"sha512": "66j5V1KVfH/QekZH9t3tpz82ffvj7kU7xAaFNtcQiapObmcat6d5H5siwPOBSIaJwa3/aJUgmHtHrU5UFz459g==", "type": "package", "path": "syncfusion.licensing/29.2.11", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/MonoAndroid90/Syncfusion.Licensing.dll", "lib/Xamarin.Mac/Syncfusion.Licensing.dll", "lib/Xamarin.iOS10/Syncfusion.Licensing.dll", "lib/net462/Syncfusion.Licensing.dll", "lib/net8.0/Syncfusion.Licensing.dll", "lib/net9.0/Syncfusion.Licensing.dll", "lib/netstandard2.0/Syncfusion.Licensing.dll", "lib/uap10.0/Syncfusion.Licensing.dll", "syncfusion.licensing.29.2.11.nupkg.sha512", "syncfusion.licensing.nuspec", "syncfusion_logo.png"]}, "Syncfusion.SfSkinManager.WPF/29.2.11": {"sha512": "GQpQjs6/MIV9QA0APQW+npT7jcGv4f6Io/AFQMv7umn2Rhh/edoLsCw+ReiZBwcNdJDMIOsejjZaU/5uSZ5EdQ==", "type": "package", "path": "syncfusion.sfskinmanager.wpf/29.2.11", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net462/Syncfusion.SfSkinManager.WPF.dll", "lib/net462/Syncfusion.SfSkinManager.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "syncfusion.sfskinmanager.wpf.29.2.11.nupkg.sha512", "syncfusion.sfskinmanager.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Shared.WPF/29.2.11": {"sha512": "O0L3ewoaDvfRzm5iD5geBCIunnxj0CfpPWGXGvp6+IZa2uH4aWy2c99Qt+3BHqKY/+9bqom7N2Efg8cmPjZz9g==", "type": "package", "path": "syncfusion.shared.wpf/29.2.11", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net462/Design/Syncfusion.Design.Wpf.dll", "lib/net462/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.Expression.Design.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.VisualStudio.Design.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.dll.Design.dll", "lib/net462/Syncfusion.Shared.WPF.dll", "lib/net462/Syncfusion.Shared.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Shared.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Shared.WPF.xml", "syncfusion.shared.wpf.29.2.11.nupkg.sha512", "syncfusion.shared.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.Themes.Windows11Light.WPF/29.2.11": {"sha512": "VAwHrmLEt3E+Tkh6iCXS4AD4+KB3QqFoWBLgcdpvHBdoMd79uRfo/n4oKtVXocF+Q2tahpOwbl9Qja1bidSteA==", "type": "package", "path": "syncfusion.themes.windows11light.wpf/29.2.11", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net462/Syncfusion.Themes.Windows11Light.WPF.dll", "lib/net462/Syncfusion.Themes.Windows11Light.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.Windows11Light.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.Windows11Light.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.Windows11Light.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.Windows11Light.WPF.xml", "syncfusion.themes.windows11light.wpf.29.2.11.nupkg.sha512", "syncfusion.themes.windows11light.wpf.nuspec", "syncfusion_logo.png"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Microsoft.Extensions.DependencyInjection >= 9.0.6", "Syncfusion.Shared.WPF >= 29.2.11", "Syncfusion.Themes.Windows11Light.WPF >= 29.2.11"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\DevExpress 23.2\\Components\\Offline Packages": {}, "e:\\DevExpress 24.2\\Components\\Offline Packages": {}, "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 AirMonitor\\AirMonitor\\AirMonitor.csproj", "projectName": "AirMonitor", "projectPath": "D:\\01 AirMonitor\\AirMonitor\\AirMonitor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 AirMonitor\\AirMonitor\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Syncfusion.Shared.WPF": {"target": "Package", "version": "[29.2.11, )"}, "Syncfusion.Themes.Windows11Light.WPF": {"target": "Package", "version": "[29.2.11, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}