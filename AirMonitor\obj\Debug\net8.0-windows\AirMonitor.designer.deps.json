{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.625.26613"}}}, "Syncfusion.Licensing/29.2.11": {"runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {"assemblyVersion": "29.2.11.0", "fileVersion": "29.2.11.0"}}}, "Syncfusion.SfSkinManager.WPF/29.2.11": {"dependencies": {"Syncfusion.Licensing": "29.2.11"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"assemblyVersion": "29.2.11.0", "fileVersion": "29.2.11.0"}}}, "Syncfusion.Shared.WPF/29.2.11": {"dependencies": {"Syncfusion.Licensing": "29.2.11"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"assemblyVersion": "29.2.11.0", "fileVersion": "29.2.11.0"}}}, "Syncfusion.Themes.Windows11Light.WPF/29.2.11": {"dependencies": {"Syncfusion.Licensing": "29.2.11", "Syncfusion.SfSkinManager.WPF": "29.2.11"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.Windows11Light.WPF.dll": {"assemblyVersion": "29.2.11.0", "fileVersion": "29.2.11.0"}}}}}, "libraries": {"Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Syncfusion.Licensing/29.2.11": {"type": "package", "serviceable": true, "sha512": "sha512-66j5V1KVfH/QekZH9t3tpz82ffvj7kU7xAaFNtcQiapObmcat6d5H5siwPOBSIaJwa3/aJUgmHtHrU5UFz459g==", "path": "syncfusion.licensing/29.2.11", "hashPath": "syncfusion.licensing.29.2.11.nupkg.sha512"}, "Syncfusion.SfSkinManager.WPF/29.2.11": {"type": "package", "serviceable": true, "sha512": "sha512-GQpQjs6/MIV9QA0APQW+npT7jcGv4f6Io/AFQMv7umn2Rhh/edoLsCw+ReiZBwcNdJDMIOsejjZaU/5uSZ5EdQ==", "path": "syncfusion.sfskinmanager.wpf/29.2.11", "hashPath": "syncfusion.sfskinmanager.wpf.29.2.11.nupkg.sha512"}, "Syncfusion.Shared.WPF/29.2.11": {"type": "package", "serviceable": true, "sha512": "sha512-O0L3ewoaDvfRzm5iD5geBCIunnxj0CfpPWGXGvp6+IZa2uH4aWy2c99Qt+3BHqKY/+9bqom7N2Efg8cmPjZz9g==", "path": "syncfusion.shared.wpf/29.2.11", "hashPath": "syncfusion.shared.wpf.29.2.11.nupkg.sha512"}, "Syncfusion.Themes.Windows11Light.WPF/29.2.11": {"type": "package", "serviceable": true, "sha512": "sha512-VAwHrmLEt3E+Tkh6iCXS4AD4+KB3QqFoWBLgcdpvHBdoMd79uRfo/n4oKtVXocF+Q2tahpOwbl9Qja1bidSteA==", "path": "syncfusion.themes.windows11light.wpf/29.2.11", "hashPath": "syncfusion.themes.windows11light.wpf.29.2.11.nupkg.sha512"}}}